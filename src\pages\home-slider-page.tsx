import * as React from "react";
// Optional plugin: mouse wheel scroll support
import { WheelGesturesPlugin } from "embla-carousel-wheel-gestures";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  type CarouselApi,
} from "@/components/ui/carousel";

import { transparentNavbar<PERSON>tom } from "@/atoms/navbar/transparent-navbar";
import Navbar from "@/components/common/navbar";
// import your home page sections
import Footer from "@/components/common/footer";
import ExplorerApp from "@/components/home/<USER>";
import Hero from "@/components/home/<USER>";
import LatestServices from "@/components/home/<USER>";
import Map from "@/components/home/<USER>";
import News from "@/components/home/<USER>";
import Partners from "@/components/home/<USER>";

export default function VerticalHomeSlider() {
  // const autoplay = React.useRef(
  //   Autoplay({ delay: 6000, stopOnInteraction: true }),
  // );

  const [api, setApi] = React.useState<CarouselApi | null>(null);
  const [selectedIndex, setSelectedIndex] = React.useState(0);

  // Set navbar as transparent for the slider page
  React.useEffect(() => {
    transparentNavbarAtom.update(true);

    return () => {
      transparentNavbarAtom.update(false);
    };
  }, []);

  React.useEffect(() => {
    if (!api) return;

    const onSelect = () => setSelectedIndex(api.selectedScrollSnap());
    api.on("select", onSelect);
    onSelect();

    return () => {
      api.off("select", onSelect);
    };
  }, [api]);

  return (
    <>
      {/* Fixed Navbar */}
      <div className="home-slider-navbar fixed top-0 right-0 left-0 z-[200]">
        <Navbar />
      </div>

      <div className="relative h-screen w-screen overflow-hidden">
        <Carousel
          setApi={setApi}
          orientation="vertical"
          opts={{
            loop: false,
            align: "start",
            duration: 800, // Smoother, more natural transition timing
          }}
          plugins={[WheelGesturesPlugin()]}
          className="h-screen w-screen"
        >
          <CarouselContent className="mt-0 h-screen">
            <CarouselItem className="h-screen pt-0">
              <Hero />
            </CarouselItem>
            <CarouselItem className="h-screen pt-0">
              <Map />
            </CarouselItem>
            <CarouselItem className="h-screen pt-0">
              <LatestServices />
            </CarouselItem>
            <CarouselItem className="h-screen pt-0">
              <News />
            </CarouselItem>
            <CarouselItem className="h-screen pt-0">
              <ExplorerApp />
            </CarouselItem>
            <CarouselItem className="flex h-screen flex-col pt-0">
              <div className="flex-1">
                <Partners />
              </div>
              <div className="flex-1">
                <Footer />
              </div>
            </CarouselItem>
          </CarouselContent>
        </Carousel>

        {/* Modern Navigation Bullets */}
        <div className="home-slider-bullets pointer-events-auto absolute top-1/2 right-6 flex -translate-y-1/2 flex-col gap-4">
          {[0, 1, 2, 3, 4, 5].map((index) => {
            const isActive = selectedIndex === index;
            return (
              <button
                key={index}
                type="button"
                aria-label={`Go to section ${index + 1}`}
                onClick={() => {
                  if (api) {
                    // Get the carousel container and slides
                    const carouselContent = document.querySelector(
                      '[data-slot="carousel-content"] > div',
                    ) as HTMLElement;

                    if (carouselContent) {
                      // Disable all transitions temporarily
                      carouselContent.style.transition = "none";
                      carouselContent.style.transform = `translateY(-${index * 100}vh)`;

                      // Update the selected index immediately
                      setSelectedIndex(index);

                      // Force a reflow to ensure the change takes effect
                      void carouselContent.offsetHeight;

                      // Re-enable transitions after the jump
                      setTimeout(() => {
                        carouselContent.style.transition = "";
                      }, 10);
                    }
                  }
                }}
                className={`group relative flex items-center justify-center transition-all duration-300 ease-out ${
                  isActive ? "scale-110" : "hover:scale-105"
                }`}
              >
                {/* Outer ring for active state */}
                <div
                  className={`absolute inset-0 rounded-full border-2 transition-all duration-300 ${
                    isActive
                      ? "scale-150 border-[#035859] opacity-100"
                      : "scale-100 border-white/30 opacity-0 group-hover:scale-125 group-hover:opacity-60"
                  }`}
                />

                {/* Main bullet */}
                <div
                  className={`relative h-3 w-3 rounded-full transition-all duration-300 ${
                    isActive
                      ? "bg-[#035859] shadow-lg shadow-[#035859]/30"
                      : "bg-white/60 group-hover:bg-white/90 group-hover:shadow-md"
                  }`}
                />

                {/* Inner dot for active state */}
                {isActive && (
                  <div className="absolute h-1.5 w-1.5 animate-pulse rounded-full bg-white" />
                )}
              </button>
            );
          })}
        </div>
      </div>
    </>
  );
}
